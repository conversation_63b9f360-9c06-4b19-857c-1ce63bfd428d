import React, { useState, useCallback, useRef } from 'react';
import React<PERSON>low, {
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  addEdge,
  Panel,
  ReactFlowProvider,
  MarkerType,
} from 'reactflow';
import 'reactflow/dist/style.css';

// Custom node components
const PromptNode = ({ data }) => {
  const { id, name, one_liner, short_prompt, test_cases, output_format } = data;
  const color = data.color || '#6366F1';
  
  return (
    <div className="bg-white rounded-lg shadow-lg border border-gray-200 w-64 overflow-hidden">
      <div className="p-3" style={{ backgroundColor: color }}>
        <div className="font-bold text-white text-lg truncate">{name}</div>
        <div className="text-white text-sm font-medium truncate">{one_liner}</div>
      </div>
      
      <div className="p-3 border-b border-gray-200 text-xs text-gray-700 max-h-24 overflow-y-auto">
        {short_prompt}
      </div>
      
      {test_cases && test_cases.length > 0 && (
        <div className="p-2 border-b border-gray-200 bg-gray-50">
          <div className="text-xs font-semibold text-gray-500 mb-1">Test Cases:</div>
          <div className="text-xs text-gray-600 italic">
            {test_cases.map((tc, i) => (
              <div key={i} className="mb-1 pl-2 border-l-2 border-gray-300">{tc}</div>
            ))}
          </div>
        </div>
      )}
      
      {output_format && (
        <div className="p-2 bg-gray-50">
          <div className="text-xs font-semibold text-gray-500 mb-1">Output Format:</div>
          <div className="text-xs text-gray-600 italic pl-2 border-l-2 border-gray-300">
            {output_format}
          </div>
        </div>
      )}
    </div>
  );
};

const ParallelNode = ({ data }) => {
  return (
    <div className="bg-white rounded-lg shadow border border-gray-200 p-2 flex items-center justify-center">
      <div className="font-bold text-gray-700">Parallel Processing</div>
    </div>
  );
};

const ConditionalNode = ({ data }) => {
  return (
    <div className="bg-white rounded-lg shadow border border-gray-200 p-2 flex flex-col items-center justify-center h-20 w-20">
      <div className="font-bold text-gray-700 text-center">Condition</div>
      <div className="text-xs text-gray-500 text-center">{data.condition}</div>
    </div>
  );
};

const FanNode = ({ data }) => {
  const isOut = data.type === 'fan-out';
  return (
    <div className={`bg-white rounded-lg shadow border border-gray-200 p-2 flex items-center justify-center h-14 w-14 ${isOut ? 'bg-blue-50' : 'bg-green-50'}`}>
      <div className="font-bold text-gray-700 text-xs text-center">{isOut ? 'Fan Out' : 'Fan In'}</div>
    </div>
  );
};

// Node types mapping
const nodeTypes = {
  prompt: PromptNode,
  parallel: ParallelNode,
  conditional: ConditionalNode,
  fan: FanNode,
};

// Sample data
const initialPrompts = {
  extract_intent: {
    name: "Extract Intent",
    one_liner: "Determine user intention from query",
    short_prompt: "Analyze the user query and extract the primary intent, entities, and sentiment. Categorize into one of: QUESTION, REQUEST, STATEMENT, COMMAND.",
    full_prompt: `Analyze the user query and extract the following:
1. Primary intent (what is the user trying to accomplish)
2. Entities mentioned (people, places, objects, concepts)  
3. Sentiment (positive, negative, neutral)
4. Query category (QUESTION, REQUEST, STATEMENT, COMMAND)

Return in JSON format with fields: intent, entities, sentiment, category.`,
    test_cases: ["How do I create a new account?", "The system isn't working right now."],
    output_format: `{"intent": "account_creation", "entities": ["account"], "sentiment": "neutral", "category": "QUESTION"}`,
    color: "#4F46E5",
  },
  
  generate_response: {
    name: "Generate Response",
    one_liner: "Create natural language response",
    short_prompt: "Generate a helpful, accurate response to the user query using the provided context. Response should be concise, direct, and answer the question fully.",
    full_prompt: `Generate a helpful, accurate response to the user query.

Use the provided context to inform your response.
Response should be:
- Concise and direct
- Answer the question fully
- Use natural, conversational language
- Maintain appropriate tone based on sentiment analysis

Do not include information not supported by the context.`,
    test_cases: ["Here's how to create a new account: First, click on 'Sign Up' in the top-right corner..."],
    output_format: "Natural language response text, 1-3 paragraphs",
    color: "#10B981",
  },
  
  format_response: {
    name: "Format Response",
    one_liner: "Apply formatting and structure",
    short_prompt: "Format the generated response with appropriate markup, structure, and layout to enhance readability and engagement.",
    full_prompt: `Format the generated response with appropriate:
- Paragraph breaks
- Bullet points for lists
- Bold for important terms
- Headings for sections (if applicable)
- Code blocks for technical content (if applicable)

Ensure the final output follows brand voice guidelines and maintains a consistent tone.`,
    test_cases: ["# Creating a New Account\n\n**First**, click on 'Sign Up' in the top-right corner..."],
    output_format: "Markdown formatted text",
    color: "#F59E0B",
  },
  
  retrieve_context: {
    name: "Retrieve Context",
    one_liner: "Fetch relevant information",
    short_prompt: "Search knowledge base for information relevant to the user query. Return most relevant documents ranked by relevance score.",
    full_prompt: `Search the knowledge base for information relevant to the user query.
Parameters:
- max_results: 5
- min_relevance: 0.7
- include_metadata: true

Return format:
- Array of documents
- Each document includes: content, relevance_score, source, last_updated`,
    test_cases: ["[{\"content\": \"Account creation process...\", \"relevance_score\": 0.92}]"],
    output_format: "JSON array of document objects",
    color: "#6366F1",
  },
  
  check_safety: {
    name: "Safety Check",
    one_liner: "Ensure content meets guidelines",
    short_prompt: "Analyze content for policy violations, harmful content, or inappropriate material. Flag content that doesn't meet safety standards.",
    full_prompt: `Analyze the content for:
1. Policy violations
2. Harmful instructions
3. Inappropriate material
4. Misleading information
5. Privacy concerns

Return a safety score (0-1) and flag if below 0.8.
Include specific violation categories and confidence scores.`,
    test_cases: ["SAFE: {\"score\": 0.95}", "FLAGGED: {\"score\": 0.65, \"violations\": [\"misleading_info\"]}"],
    output_format: `{"status": "SAFE|FLAGGED", "score": 0.95, "violations": []}`,
    color: "#EF4444",
  }
};

const initialMermaidDiagram = `
graph TD
    A[Extract Intent] --> B[Retrieve Context]
    A --> C{Safety Check}
    C -->|Safe| D[Generate Response]
    C -->|Flagged| E[Safety Response]
    B --> D
    D --> F[Format Response]
    
    subgraph Parallel Processing
    G[Sentiment Analysis] --> H[Tone Adjustment]
    I[Entity Enrichment] --> J[Context Enhancement]
    end
    
    A --> Parallel Processing
    Parallel Processing --> D
`;

// Parse mermaid diagram and create nodes and edges
const parseMermaidToFlow = (diagram, prompts) => {
  // This is a simplified parser - in a real application, you would use a proper parser
  const lines = diagram.trim().split('\n').filter(line => line.trim() && !line.includes("graph TD") && !line.includes("subgraph"));
  
  const nodes = [];
  const edges = [];
  let position = {x: 50, y: 50};
  const positions = {};
  
  lines.forEach((line, index) => {
    line = line.trim();
    
    // Handle nodes: A[Label]
    if (line.includes('[') && line.includes(']') && !line.includes('-->')) {
      const match = line.match(/([A-Z]+)\[(.*?)\]/);
      if (match) {
        const id = match[1];
        const label = match[2];
        
        // Find matching prompt
        let promptData = null;
        Object.entries(prompts).forEach(([promptId, data]) => {
          if (data.name === label) {
            promptData = { id: promptId, ...data };
          }
        });
        
        if (!promptData) {
          // Create a basic node if no matching prompt found
          promptData = {
            id: id.toLowerCase(),
            name: label,
            one_liner: "No description available",
            short_prompt: "No prompt available",
          };
        }
        
        position.y += index * 180;
        positions[id] = {...position};
        
        nodes.push({
          id: id,
          type: 'prompt',
          data: promptData,
          position: positions[id],
        });
      }
    }
    
    // Handle conditional nodes: C{Condition}
    if (line.includes('{') && line.includes('}') && !line.includes('-->')) {
      const match = line.match(/([A-Z]+)\{(.*?)\}/);
      if (match) {
        const id = match[1];
        const condition = match[2];
        
        position.y += index * 180;
        positions[id] = {...position};
        
        nodes.push({
          id: id,
          type: 'conditional',
          data: { condition },
          position: positions[id],
        });
      }
    }
    
    // Handle edges: A --> B
    if (line.includes('-->')) {
      const parts = line.split('-->');
      const source = parts[0].trim();
      let target = parts[1].trim();
      let label = '';
      
      // Handle edges with labels: A -->|Label| B
      if (target.includes('|')) {
        const labelParts = target.split('|');
        label = labelParts[0];
        target = labelParts[1];
      }
      
      edges.push({
        id: `${source}-${target}`,
        source: source,
        target: target,
        label: label,
        type: 'smoothstep',
        markerEnd: {
          type: MarkerType.ArrowClosed,
        },
      });
    }
  });
  
  return { nodes, edges };
};

// Initial flow based on mermaid diagram
const initialFlow = parseMermaidToFlow(initialMermaidDiagram, initialPrompts);

const PromptFlow = () => {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialFlow.nodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialFlow.edges);
  const [selectedNode, setSelectedNode] = useState(null);
  const [mermaidSource, setMermaidSource] = useState(initialMermaidDiagram);
  const [showMermaidEditor, setShowMermaidEditor] = useState(false);
  
  const reactFlowWrapper = useRef(null);
  
  const onConnect = useCallback((params) => setEdges((eds) => addEdge(params, eds)), [setEdges]);
  
  const onNodeClick = useCallback((_, node) => {
    setSelectedNode(node);
  }, []);
  
  const updateNodeData = (data) => {
    setNodes(nds => {
      return nds.map(n => {
        if (n.id === selectedNode.id) {
          return {
            ...n,
            data: {
              ...n.data,
              ...data,
            },
          };
        }
        return n;
      });
    });
  };
  
  const closeSidebar = () => {
    setSelectedNode(null);
  };
  
  const updateFromMermaid = () => {
    const newFlow = parseMermaidToFlow(mermaidSource, initialPrompts);
    setNodes(newFlow.nodes);
    setEdges(newFlow.edges);
    setShowMermaidEditor(false);
  };
  
  return (
    <div className="flex h-screen w-full">
      <div className={`flex-1 ${selectedNode ? 'w-2/3' : 'w-full'}`}>
        <ReactFlowProvider>
          <div ref={reactFlowWrapper} className="h-full w-full">
            <ReactFlow
              nodes={nodes}
              edges={edges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              onConnect={onConnect}
              onNodeClick={onNodeClick}
              nodeTypes={nodeTypes}
              fitView
            >
              <Controls />
              <MiniMap />
              <Background color="#f0f0f0" gap={16} />
              <Panel position="top-right">
                <button 
                  className="px-4 py-2 bg-blue-600 text-white rounded shadow mr-2"
                  onClick={() => setShowMermaidEditor(!showMermaidEditor)}
                >
                  {showMermaidEditor ? 'Hide Mermaid' : 'Edit Mermaid'}
                </button>
                <button className="px-4 py-2 bg-green-600 text-white rounded shadow">
                  Save Flow
                </button>
              </Panel>
              
              {showMermaidEditor && (
                <Panel position="top-left" className="w-1/2">
                  <div className="p-4 bg-white rounded shadow-lg border border-gray-200">
                    <h3 className="font-bold mb-2">Mermaid Diagram Editor</h3>
                    <textarea 
                      className="w-full h-64 font-mono text-sm p-2 border rounded"
                      value={mermaidSource}
                      onChange={(e) => setMermaidSource(e.target.value)}
                    />
                    <div className="flex justify-end mt-2">
                      <button 
                        className="px-3 py-1 bg-gray-200 text-gray-800 rounded mr-2"
                        onClick={() => setShowMermaidEditor(false)}
                      >
                        Cancel
                      </button>
                      <button 
                        className="px-3 py-1 bg-blue-600 text-white rounded"
                        onClick={updateFromMermaid}
                      >
                        Apply
                      </button>
                    </div>
                  </div>
                </Panel>
              )}
            </ReactFlow>
          </div>
        </ReactFlowProvider>
      </div>
      
      {selectedNode && (
        <div className="w-1/3 border-l border-gray-200 overflow-y-auto">
          <div className="p-4">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold">Edit Prompt</h2>
              <button 
                className="p-1 rounded-full hover:bg-gray-200"
                onClick={closeSidebar}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            {selectedNode.type === 'prompt' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Prompt ID</label>
                  <div className="text-sm text-gray-900 font-mono bg-gray-100 p-2 rounded">
                    {selectedNode.data.id}
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Display Name</label>
                  <input 
                    type="text" 
                    className="w-full p-2 border rounded"
                    value={selectedNode.data.name}
                    onChange={(e) => updateNodeData({ name: e.target.value })}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">One-liner</label>
                  <input 
                    type="text" 
                    className="w-full p-2 border rounded"
                    value={selectedNode.data.one_liner}
                    onChange={(e) => updateNodeData({ one_liner: e.target.value })}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Short Prompt</label>
                  <textarea 
                    className="w-full p-2 border rounded h-20"
                    value={selectedNode.data.short_prompt}
                    onChange={(e) => updateNodeData({ short_prompt: e.target.value })}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Full Prompt</label>
                  <textarea 
                    className="w-full p-2 border rounded h-40 font-mono text-sm"
                    value={selectedNode.data.full_prompt}
                    onChange={(e) => updateNodeData({ full_prompt: e.target.value })}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Test Cases</label>
                  <textarea 
                    className="w-full p-2 border rounded h-20"
                    value={selectedNode.data.test_cases?.join('\n')}
                    onChange={(e) => updateNodeData({ test_cases: e.target.value.split('\n') })}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Output Format</label>
                  <textarea 
                    className="w-full p-2 border rounded h-20 font-mono text-sm"
                    value={selectedNode.data.output_format}
                    onChange={(e) => updateNodeData({ output_format: e.target.value })}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Color</label>
                  <div className="flex items-center">
                    <input 
                      type="color" 
                      className="p-1 border rounded mr-2 w-12 h-8"
                      value={selectedNode.data.color || "#6366F1"}
                      onChange={(e) => updateNodeData({ color: e.target.value })}
                    />
                    <span className="text-sm font-mono">{selectedNode.data.color || "#6366F1"}</span>
                  </div>
                </div>
                
                <div className="pt-4">
                  <button className="px-4 py-2 bg-blue-600 text-white rounded shadow w-full">
                    Save Changes
                  </button>
                </div>
              </div>
            )}
            
            {selectedNode.type === 'conditional' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Condition</label>
                  <input 
                    type="text" 
                    className="w-full p-2 border rounded"
                    value={selectedNode.data.condition}
                    onChange={(e) => updateNodeData({ condition: e.target.value })}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default function App() {
  return (
    <div className="w-full h-screen bg-gray-50">
      <PromptFlow />
    </div>
  );
}