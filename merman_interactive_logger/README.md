# MARS Interactive Logger

A simple, colorful, and readable logger for the MARS (Multi-Agent Reasoning System) prompting framework.

## Features

✨ **Color-coded output** by agent type  
📦 **Clean box formatting** for better readability  
📊 **Progress tracking** with bars and step indicators  
✂️ **Smart text truncation** to prevent overwhelming output  
🎯 **Highlight improvements** with score tracking  

## Quick Integration

Replace the print statements in your MARS system with logger calls:

```python
from merman_interactive_logger import MarsLogger

# Initialize logger
logger = MarsLogger(max_text_length=120, use_colors=True)

# In your Manager class:
def run(self, dataset, original_prompt, iterations=1):
    task, current_prompt = self.user_proxy.run()
    
    # Replace: print("Starting MARS procedure...")
    logger.start_procedure(f"Task: {task}")
    
    steps = self.planner.run()
    # Replace: print(f"Planner generated {len(steps)} steps: {steps}")
    logger.log_planner(steps)
    
    for iteration in range(iterations):
        # Replace: print(f"\n--- Iteration {iteration+1}/{iterations} ---")
        logger.log_iteration_start(iteration+1, iterations)
        
        for step_idx, step in enumerate(steps):
            # Replace: print(f"  Step {step_idx+1}/{len(steps)}: {step}")
            logger.log_step_start(step_idx+1, len(steps), step)
            
            teacher_question = self.teacher.run(step, prompt_for_this_iteration)
            # Replace: print(f"    Teacher question: {teacher_question}")
            logger.log_teacher_question(teacher_question)
            
            is_socratic, suggestion = self.critic.run(teacher_question)
            # Replace critic print statements
            logger.log_critic_evaluation(is_socratic, suggestion)
            
            if not is_socratic:
                adjusted_question = self.teacher.adjust_question(...)
                # Replace: print(f"    Teacher (adjusted) question: {adjusted_question}")
                logger.log_teacher_adjustment(adjusted_question)
            
            optimized_prompt = self.student.run(...)
            # Replace: print(f"    Student optimized prompt: {optimized_prompt}")
            logger.log_student_response(optimized_prompt)
            
        current_score = self.target.run(...)
        is_best = current_score > best_overall_score
        # Replace: print(f"  Iteration {iteration+1} evaluation score: {current_score:.4f}")
        logger.log_evaluation_score(current_score, is_best)
    
    # Replace final print statements
    logger.log_final_results(best_overall_prompt, best_overall_score)
```

## Demo

Run the demo to see the logger in action:

```bash
cd merman_interactive_logger
python demo.py
```

## Configuration

```python
logger = MarsLogger(
    max_text_length=120,  # Truncate long text
    use_colors=True       # Enable/disable colors
)
```

## Agent Color Scheme

- 🎓 **Teacher**: Blue (questions and adjustments)
- ⚖️ **Critic**: Green (✅) / Yellow (❌) (evaluations)  
- 📝 **Student**: Green (optimized prompts)
- 📋 **Planner**: Purple (optimization steps)
- 🎯 **Scores**: White / Bold Green (improvements)

Perfect for making MARS output much more readable and engaging! 