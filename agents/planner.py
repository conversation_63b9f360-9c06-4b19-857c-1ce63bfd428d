import openai
import os


class Planner:
    def __init__(self, task):
        self.task = task

    def run(self):
        system = (
            "You are a task planner. Given a task description, split it into a sequence of detailed, logical steps for prompt optimization. "
            "Output the steps as a numbered list, one per line."
        )
        prompt = f"Task: {self.task}\nSplit this task into detailed steps."
        openai.api_key = os.getenv("OPENAI_API_KEY")
        response = openai.chat.completions.create(
            model="gpt-4.1",
            messages=[
                {"role": "system", "content": system},
                {"role": "user", "content": prompt},
            ],
            max_tokens=256,
            temperature=0.3,
        )
        content = response.choices[0].message.content.strip()
        # Parse steps from the response
        steps = [
            line.split(".", 1)[-1].strip()
            for line in content.split("\n")
            if line.strip()
        ]
        return steps
