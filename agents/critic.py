import openai
import os


class Critic:
    def run(self, question):
        system = (
            "You are an evaluator responsible for judging the Socratic style of a question. "
            "Your output must strictly follow these rules: 1. If the question is Socratic, output only: [True] "
            "2. If the question is not Socratic, output: [False] [suggestion: <reason for the incorrect judgment>] "
            "Replace '<reason for the incorrect judgment>' with a clear and concise explanation. "
            "Do not include any additional text, comments, or explanations beyond the specified format."
        )
        prompt = f"Question: {question}"
        openai.api_key = os.getenv("OPENAI_API_KEY")
        response = openai.chat.completions.create(
            model="gpt-4.1",
            messages=[
                {"role": "system", "content": system},
                {"role": "user", "content": prompt},
            ],
            max_tokens=128,
            temperature=0.0,
        )
        result = response.choices[0].message.content.strip()

        if result.startswith("[True]"):
            return True, None
        elif result.startswith("[False]"):
            suggestion_text = result.replace("[False]", "", 1).strip()
            if suggestion_text.startswith("[suggestion:") and suggestion_text.endswith(
                "]"
            ):
                suggestion_text = suggestion_text[len("[suggestion:") : -1].strip()
            elif not suggestion_text:
                suggestion_text = "The question was deemed not Socratic, but no specific reason was provided by the evaluator."
            return False, suggestion_text
        else:
            print(f"Warning: Critic LLM returned unexpected format: {result}")
            return (
                False,
                "The evaluator's response was in an unexpected format. The original question may need review.",
            )
