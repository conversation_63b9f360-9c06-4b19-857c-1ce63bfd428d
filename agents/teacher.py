import openai
import os


class Teacher:
    def run(self, step, prev_answer):
        system = (
            "You are a teacher who asks questions in the Socratic manner based on objectives and student responses. "
            "Please ask a total of two questions: The first one is for the problem that appeared in the prompt given by the students in the last round. "
            "The second one is an optimization solution based on the current steps of the task. "
            "Please include only questions in your output and do not make answers for your students."
        )
        prompt = (
            f"Current step: {step}\nPrevious student answer (prompt): {prev_answer}"
        )
        openai.api_key = os.getenv("OPENAI_API_KEY")
        response = openai.chat.completions.create(
            model="gpt-4.1",
            messages=[
                {"role": "system", "content": system},
                {"role": "user", "content": prompt},
            ],
            max_tokens=256,
            temperature=0.7,
        )
        return response.choices[0].message.content.strip()

    def adjust_question(
        self, original_question, critic_suggestion, current_step, student_prompt
    ):
        # This method should be implemented by a subclass (e.g., OpenAITeacher)
        # It will use an LLM to revise the original_question based on critic_suggestion
        raise NotImplementedError(
            "adjust_question must be implemented by a subclass of Teacher."
        )
