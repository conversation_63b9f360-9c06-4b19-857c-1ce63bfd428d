import openai
import os
from agents.manager import Manager
from agents.user_proxy import UserProxy
from agents.planner import Planner
from agents.teacher import Teacher
from agents.critic import Critic
from agents.student import Student
from agents.target import Target

# Set your OpenAI API key
openai.api_key = os.getenv("OPENAI_API_KEY")  # API key is set within each agent now
openai.api_type = "openai"


def openai_chat(model, messages, max_tokens, temperature, system_prompt_override=None):
    # Centralized OpenAI call function
    # Note: openai.api_key should be set before calling this, typically within the agent method.
    # model, messages, max_tokens, temperature are passed directly.
    # system_prompt_override is not used here as system prompt is part of messages.
    return (
        openai.chat.completions.create(
            model=model,
            messages=messages,
            max_tokens=max_tokens,
            temperature=temperature,
        )
        .choices[0]
        .message.content.strip()
    )


class OpenAITeacher(Teacher):
    def run(self, step, prev_answer):
        system = (
            "You are a teacher who asks questions in the Socratic manner based on objectives and student responses. "
            "Please ask a total of two questions: The first one is for the problem that appeared in the prompt given by the students in the last round. "
            "The second one is an optimization solution based on the current steps of the task. "
            "Please include only questions in your output and do not make answers for your students."
        )
        prompt = (
            f"Current step: {step}\nPrevious student answer (prompt): {prev_answer}"
        )
        openai.api_key = os.getenv("OPENAI_API_KEY")
        return openai_chat(
            model="gpt-4.1",
            messages=[
                {"role": "system", "content": system},
                {"role": "user", "content": prompt},
            ],
            max_tokens=256,
            temperature=0.7,
        )

    def adjust_question(
        self, original_question, critic_suggestion, current_step, student_prompt
    ):
        system = (
            "You are a teacher. You previously asked a question that was criticized for not being sufficiently Socratic. "
            "Your task is to revise the original question based on the critic's suggestion. "
            "Keep the context of the current optimization step and the student's last prompt in mind. "
            "Output only the revised Socratic question(s)."
        )
        prompt = (
            f"Original Question:\n{original_question}\n\n"
            f"Critic's Suggestion:\n{critic_suggestion}\n\n"
            f"Context - Current Optimization Step:\n{current_step}\n\n"
            f"Context - Student's Last Prompt:\n{student_prompt}\n\n"
            f"Please provide the revised Socratic question(s):"
        )
        openai.api_key = os.getenv("OPENAI_API_KEY")
        return openai_chat(
            model="gpt-4.1",
            messages=[
                {"role": "system", "content": system},
                {"role": "user", "content": prompt},
            ],
            max_tokens=256,
            temperature=0.7,
        )


class OpenAICritic(Critic):
    def run(self, question):
        # Implementation is in agents/critic.py
        # This class in main.py is acting as a wrapper if needed for specific OpenAI config,
        # but the core logic is now directly in agents/critic.py which uses OpenAI.
        # For consistency, we can keep this structure or simplify if OpenAICritic is not adding unique behavior here.
        # For now, let's assume agents/critic.py handles its own OpenAI calls.
        # If main.py's OpenAICritic needs to differ, implement here.
        # To avoid confusion, better to remove this if not needed and use Critic from agents.critic directly.
        # For now, creating an instance of the base Critic which uses OpenAI
        base_critic = Critic()
        return base_critic.run(question)


class OpenAIStudent(Student):
    def run(self, current_prompt, teacher_question):
        # Implementation is in agents/student.py
        # Similar to OpenAICritic, this can be simplified.
        # Creating an instance of the base Student which uses OpenAI
        base_student = Student()
        return base_student.run(current_prompt, teacher_question)


def main():
    # Example task and prompt
    task = "Given a full SVG path element containing multiple commands, determine the geometric shape that would be generated if one were to execute the full path element."
    original_prompt = "Let's think step by step."

    # Example dataset for the SVG task (you'll need a real, larger one)
    svg_example_path_square = '<path d="M 10 10 H 90 V 90 H 10 L 10 10"/>'  # A square
    svg_example_path_triangle = '<path d="M100,100 L200,100 L150,150 Z"/>'  # A triangle
    svg_example_path_line = '<path d="M 10 10 L 90 90"/>'  # A line

    dataset = [
        (svg_example_path_square, "square"),
        (svg_example_path_triangle, "triangle"),
        (svg_example_path_line, "line"),
        # Add more (input_x, gold_label_y) pairs for robust evaluation
    ]

    user_proxy = UserProxy(task, original_prompt)
    planner = Planner(
        task
    )  # Planner is now directly using OpenAI from agents/planner.py
    teacher = OpenAITeacher()
    critic = Critic()  # Critic is now directly using OpenAI from agents/critic.py
    student = Student()  # Student is now directly using OpenAI from agents/student.py
    target = Target()
    manager = Manager(user_proxy, planner, teacher, critic, student, target)

    # Run the MARS procedure
    print("Starting MARS procedure...")
    best_prompt = manager.run(
        dataset, original_prompt, iterations=2
    )  # Example with 2 iterations
    print(f"\nMARS procedure finished. \nBest prompt found: {best_prompt}")


if __name__ == "__main__":
    main()
